<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.totalDraws || 0 }}</div>
            <div class="statistics-label">总抽奖次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.totalWins || 0 }}</div>
            <div class="statistics-label">总中奖次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.winRate || '0.00' }}%</div>
            <div class="statistics-label">中奖率</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-number">{{ statistics.unclaimedCount || 0 }}</div>
            <div class="statistics-label">未领取奖品</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动名称" prop="activityName">
        <el-input v-model="queryParams.activityName" placeholder="请输入活动名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userNickname">
        <el-input v-model="queryParams.userNickname" placeholder="请输入用户昵称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="是否中奖" prop="isWinner">
        <el-select v-model="queryParams.isWinner" placeholder="是否中奖" clearable>
          <el-option label="中奖" value="1" />
          <el-option label="未中奖" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="领取状态" prop="claimStatus">
        <el-select v-model="queryParams.claimStatus" placeholder="领取状态" clearable>
          <el-option label="已领取" value="1" />
          <el-option label="未领取" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="抽奖时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['lottery:record:export']">导出</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column label="记录ID" align="center" prop="recordId" width="80" />
      <el-table-column label="活动名称" align="center" prop="activityName" :show-overflow-tooltip="true" />
      <el-table-column label="用户昵称" align="center" prop="userNickname" :show-overflow-tooltip="true" />
      <el-table-column label="奖品名称" align="center" prop="prizeName" :show-overflow-tooltip="true" />
      <el-table-column label="奖品类型" align="center" prop="prizeType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.prizeType === 'physical'" type="primary">实物奖品</el-tag>
          <el-tag v-else-if="scope.row.prizeType === 'coupon'" type="success">优惠券</el-tag>
          <el-tag v-else-if="scope.row.prizeType === 'points'" type="warning">积分</el-tag>
          <el-tag v-else type="info">谢谢参与</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="奖品价值" align="center" prop="prizeValue" />
      <el-table-column label="是否中奖" align="center" prop="isWinner">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isWinner === '1'" type="success">中奖</el-tag>
          <el-tag v-else type="info">未中奖</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="领取状态" align="center" prop="claimStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.claimStatus === '1'" type="success">已领取</el-tag>
          <el-tag v-else-if="scope.row.isWinner === '1'" type="warning">未领取</el-tag>
          <el-tag v-else type="info">-</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="抽奖时间" align="center" prop="drawTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.drawTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领取时间" align="center" prop="claimTime" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.claimTime ? parseTime(scope.row.claimTime, '{y}-{m}-{d} {h}:{i}') : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isWinner === '1' && scope.row.claimStatus === '0'" size="mini" type="text"
            icon="el-icon-check" @click="handleClaim(scope.row)" v-hasPermi="['lottery:record:edit']">标记已领取</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['lottery:record:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  listLotteryRecord,
  delLotteryRecord,
  claimPrize,
  exportLotteryRecord,
  getLotteryStatistics
} from "@/api/lottery/record";
import { getMerchantId } from "@/api/login";

export default {
  name: "LotteryRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 抽奖记录表格数据
      recordList: [],
      // 日期范围
      dateRange: [],
      // 统计信息
      statistics: {},
      // 当前用户商家ID
      currentMerchantId: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityName: null,
        userNickname: null,
        isWinner: null,
        claimStatus: null,
      }
    };
  },
  async created() {
    // 从路由参数获取活动ID
    if (this.$route.query.activityId) {
      this.queryParams.activityId = this.$route.query.activityId;
    }

    // 获取当前用户的商家ID
    await this.getCurrentMerchantId();

    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询抽奖记录列表 */
    getList() {
      this.loading = true;

      // 构建查询参数，包含商家ID
      const listParams = {
        ...this.queryParams,
        merchantId: this.currentMerchantId
      };

      listLotteryRecord(this.addDateRange(listParams, this.dateRange)).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取记录列表失败:', error);
        this.loading = false;
      });
    },
    /** 获取当前用户商家ID */
    async getCurrentMerchantId() {
      try {
        const response = await getMerchantId();
        this.currentMerchantId = response.data;
      } catch (error) {
        console.error('获取商家ID失败:', error);
        this.$modal.msgError("获取商家信息失败");
      }
    },
    /** 获取统计信息 */
    getStatistics() {
      // 构建统计查询参数，包含商家ID
      const statisticsParams = {
        ...this.queryParams,
        merchantId: this.currentMerchantId
      };

      getLotteryStatistics(statisticsParams).then(response => {
        this.statistics = response.data || {};
      }).catch(error => {
        console.error('获取统计信息失败:', error);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.getStatistics();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 标记已领取按钮操作 */
    handleClaim(row) {
      const recordId = row.recordId;
      this.$modal.confirm('是否确认标记奖品"' + row.prizeName + '"为已领取？').then(function () {
        return claimPrize(recordId);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("标记成功");
      }).catch(() => { });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$modal.confirm('是否确认删除抽奖记录编号为"' + recordIds + '"的数据项？').then(function () {
        return delLotteryRecord(recordIds);
      }).then(() => {
        this.getList();
        this.getStatistics();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('lottery/record/export', {
        ...this.queryParams
      }, `record_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.statistics-card {
  margin-bottom: 20px;
}

.statistics-content {
  text-align: center;
  padding: 20px 0;
}

.statistics-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
}
</style>
