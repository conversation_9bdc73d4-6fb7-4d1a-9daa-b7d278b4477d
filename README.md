# 抽奖点餐系统

基于若依框架开发的抽奖点餐系统，支持商家管理、桌台管理、抽奖活动等功能。

## 🎯 项目概述

本系统是一个集成了点餐和抽奖功能的小程序系统，主要面向餐饮商家，提供完整的商家管理后台和用户小程序端。

### 主要特色

- 🏪 **商家管理**：支持多商家管理，包含到期时间控制
- 🍽️ **点餐功能**：集成美团点餐小程序链接
- 🎲 **抽奖系统**：转盘抽奖，支持多种奖品配置
- 📱 **小程序端**：用户扫码即可参与点餐和抽奖
- ⚙️ **配置管理**：灵活的 UI 配置和业务配置

## 🚀 功能特性

### 后台管理系统

#### 1. 超级管理员功能

- **商家管理**：添加商家、设置使用时间、状态管理
- **商家用户管理**：为商家创建管理账号
- **系统监控**：查看系统运行状态和统计数据
- **到期提醒**：自动检测即将到期的商家

#### 2. 商家管理员功能

- **基本信息管理**：修改商家信息、上传微信二维码
- **桌台管理**：添加桌台、配置美团点餐链接、生成桌台二维码
- **抽奖配置**：设置领取说明、每日抽奖次数限制
- **UI 配置**：自定义小程序界面样式、主题色彩、背景图片
- **数据统计**：查看抽奖数据、用户参与情况

### 小程序端

#### 1. 首页功能

- **扫码进入**：扫描桌台二维码自动识别商家和桌台
- **点餐按钮**：跳转到美团点餐小程序
- **抽奖按钮**：进入抽奖页面参与活动
- **商家信息展示**：显示商家名称、地址、微信二维码

#### 2. 抽奖功能

- **转盘抽奖**：精美的转盘动画效果
- **实时验证**：检查商家状态和剩余抽奖次数
- **中奖展示**：动画展示中奖结果
- **奖品领取**：显示领取说明和领取流程

#### 3. 个人中心

- **抽奖记录**：查看历史抽奖记录
- **奖品管理**：查看已中奖品和领取状态
- **分享功能**：分享中奖喜悦

### 商家到期检查机制

- **自动检查**：用户扫码时自动检查商家是否过期
- **友好提示**：过期时显示提示信息，引导联系商家续期
- **状态管理**：支持正常、停用、过期三种状态

## 🛠️ 技术栈

### 后端技术

- **框架**：Spring Boot 2.5.x
- **安全**：Spring Security + JWT
- **数据库**：MySQL 8.0 + MyBatis
- **缓存**：Redis
- **工具**：Lombok、Hutool

### 前端技术

- **管理后台**：Vue 2.x + Element UI
- **小程序**：uni-app + Vue 2.x
- **构建工具**：Webpack、Vite

### 开发工具

- **IDE**：IntelliJ IDEA、HBuilderX
- **版本控制**：Git
- **接口文档**：Swagger

## 📦 项目结构

```
prize-draw-order/
├── prize-draw-order-ruoyi/     # 后台管理系统
│   ├── ruoyi-admin/            # 启动模块
│   ├── ruoyi-common/           # 通用模块
│   ├── ruoyi-framework/        # 框架核心
│   ├── ruoyi-system/           # 系统模块
│   ├── ruoyi-ui/              # 前端管理界面
│   └── sql/                   # 数据库脚本
├── prize-draw-order-uniapp/    # 小程序端
│   ├── pages/                 # 页面文件
│   ├── components/            # 组件文件
│   ├── utils/                 # 工具类
│   └── static/                # 静态资源
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 环境要求

- JDK 1.8+
- MySQL 8.0+
- Redis 3.0+
- Node.js 14+
- 微信开发者工具

### 安装步骤

#### 1. 克隆项目

```bash
git clone [项目地址]
cd prize-draw-order
```

#### 2. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE prize_draw_order CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据
USE prize-draw-order;
SOURCE prize-draw-order-ruoyi/sql/prize-draw-order.sql;
```

#### 3. 后端配置

```yaml
# 修改 ruoyi-admin/src/main/resources/application-druid.yml
spring:
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: your_password

# 修改 Redis 配置
redis:
  host: localhost
  port: 6379
  password: your_redis_password
```

#### 4. 启动后端服务

```bash


#### 5. 启动前端管理界面

```bash
cd prize-draw-order-ruoyi/ruoyi-ui
npm install
npm run dev
```

#### 6. 配置小程序

```bash
cd prize-draw-order-uniapp
npm install

# API配置已设置为 http://localhost:18080
# 如需修改，请编辑 utils/api.js 中的 API_BASE_URL
```

#### 7. 运行小程序

- 使用 HBuilderX 打开 `prize-draw-order-uniapp` 目录
- 运行到微信开发者工具
- 在微信开发者工具中预览

### 🔧 故障排除

如果遇到 UniApp 中奖记录页面查询报错，请参考：

### 默认账号

- **超级管理员**：admin / admin123
- **商家管理员**：需要通过超级管理员创建

## 📋 核心业务流程

### 商家入驻流程

1. 超级管理员添加商家信息
2. 设置商家使用期限
3. 为商家创建管理员账号
4. 商家管理员登录配置系统

### 桌台配置流程

1. 商家管理员添加桌台信息
2. 配置美团点餐链接
3. 生成桌台二维码
4. 打印二维码放置在桌台

### 用户使用流程

1. 用户扫描桌台二维码
2. 进入小程序首页
3. 选择点餐或抽奖功能
4. 参与抽奖并查看结果
5. 根据说明领取奖品

## 🔧 配置说明

### 商家配置项

- **基本信息**：商家名称、联系方式、地址、微信二维码
- **抽奖配置**：领取说明、每日抽奖次数、活动开关
- **UI 配置**：主题色彩、背景图片、Logo、欢迎语
- **高级配置**：自定义 CSS、JavaScript、统计代码

### 系统配置项

- **到期检查**：自动检查商家到期状态
- **权限控制**：基于角色的权限管理
- **数据统计**：抽奖数据、用户行为统计

## 🚀 部署指南

### 生产环境部署

#### 1. 后端部署

```bash
# 打包项目
mvn clean package -Dmaven.test.skip=true

# 运行jar包
java -jar ruoyi-admin.jar --spring.profiles.active=prod
```

#### 2. 前端部署

```bash
# 打包前端
npm run build:prod

# 配置Nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 3. 小程序发布

1. 在 HBuilderX 中选择"发行" -> "小程序-微信"
2. 配置小程序 AppID
3. 上传到微信公众平台
4. 提交审核并发布

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues：[GitHub Issues](项目地址/issues)
- 邮箱：<EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：

- [RuoYi](https://gitee.com/y_project/RuoYi) - 后台管理系统框架
- [uni-app](https://uniapp.dcloud.io/) - 跨平台应用开发框架
- [Element UI](https://element.eleme.io/) - Vue.js 2.0 的桌面端组件库

## 📄 许可证

本项目基于 MIT 许可证开源。
