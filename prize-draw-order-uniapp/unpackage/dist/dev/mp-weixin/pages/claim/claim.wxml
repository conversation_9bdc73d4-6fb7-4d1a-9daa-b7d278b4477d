<view class="claim-container data-v-011e7c40" style="{{('background:'+backgroundGradient)}}"><block wx:if="{{isLoading}}"><view class="loading-container data-v-011e7c40"><view class="loading-icon data-v-011e7c40">⏳</view><view class="loading-text data-v-011e7c40">加载中...</view></view></block><block wx:if="{{!isLoading&&recordDetail}}"><view class="claim-content data-v-011e7c40"><view class="prize-section data-v-011e7c40"><view class="prize-icon data-v-011e7c40">🎁</view><view class="prize-title data-v-011e7c40">恭喜您获得</view><view class="prize-name data-v-011e7c40">{{recordDetail.prizeName}}</view><block wx:if="{{recordDetail.prizeValue}}"><view class="prize-desc data-v-011e7c40">{{recordDetail.prizeValue}}</view></block></view><view class="record-info data-v-011e7c40"><view class="info-item data-v-011e7c40"><text class="label data-v-011e7c40">中奖时间：</text><text class="value data-v-011e7c40">{{$root.m0}}</text></view><view class="info-item data-v-011e7c40"><text class="label data-v-011e7c40">记录编号：</text><text class="value data-v-011e7c40">{{recordDetail.recordId}}</text></view><view class="info-item data-v-011e7c40"><text class="label data-v-011e7c40">领取状态：</text><text class="{{['value','status','data-v-011e7c40',(recordDetail.claimStatus==='1')?'claimed':'']}}">{{''+(recordDetail.claimStatus==='1'?'已领取':'待领取')+''}}</text></view></view><block wx:if="{{claimInstruction}}"><view class="claim-instruction data-v-011e7c40"><view class="instruction-title data-v-011e7c40">领取说明</view><view class="instruction-content data-v-011e7c40">{{claimInstruction}}</view></view></block><block wx:if="{{activityInfo&&activityInfo.wechatQrcode}}"><view class="wechat-qrcode data-v-011e7c40"><image class="qrcode-img data-v-011e7c40" src="{{$root.m1}}" mode="aspectFit" data-event-opts="{{[['error',[['handleImageError',['$event']]]],['load',[['handleImageLoad',['$event']]]]]}}" binderror="__e" bindload="__e"></image><block wx:if="{{imageLoadError}}"><view class="qrcode-error data-v-011e7c40"><text class="data-v-011e7c40">二维码加载失败</text></view></block></view></block><view class="action-buttons data-v-011e7c40"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="btn btn-secondary data-v-011e7c40" bindtap="__e"><text class="data-v-011e7c40">返回</text></view></view></view></block><block wx:if="{{!isLoading&&!recordDetail}}"><view class="error-container data-v-011e7c40"><view class="error-icon data-v-011e7c40">❌</view><view class="error-text data-v-011e7c40">记录不存在或已过期</view><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="btn btn-primary data-v-011e7c40" bindtap="__e"><text class="data-v-011e7c40">返回</text></view></view></block></view>